import os
import random
import shutil
import yaml


project_name = "dataset_aec_pack_0805_1204_mix"  # 项目名称

# 原始目录
image_dir = '/Users/<USER>/Downloads/temp_aec_pack_0731_20250805_113606/images'
label_dir = '/Users/<USER>/Downloads/temp_aec_pack_0731_20250805_113606/labels'

# 输出目录  yolo task=detect mode=train model=yolov8n.pt data=/Users/<USER>/Documents/Workspace/line_detect/test001/test001.yaml epochs=100 imgsz=640 batch=16
train_image_dir = f'projects/{project_name}/dataset/images/train'
val_image_dir = f'projects/{project_name}/dataset/images/val'
train_label_dir = f'projects/{project_name}/dataset/labels/train'
val_label_dir = f'projects/{project_name}/dataset/labels/val'

# 创建目录
os.makedirs(train_image_dir, exist_ok=True)
os.makedirs(val_image_dir, exist_ok=True)
os.makedirs(train_label_dir, exist_ok=True)
os.makedirs(val_label_dir, exist_ok=True)

# 获取所有标签文件名称（以label_dir为基准）
labels = [f for f in os.listdir(label_dir) if f.endswith('.txt')]
random.shuffle(labels)

# 8:2 划分
split_index = int(0.8 * len(labels))
train_labels = labels[:split_index]
val_labels = labels[split_index:]

# 复制文件
for label in train_labels:
    label_path = os.path.join(label_dir, label)
    img = label.replace('.txt', '.jpg')
    img_path = os.path.join(image_dir, img)
    
    # 检查图像和标签文件是否都存在
    if os.path.exists(label_path) and os.path.exists(img_path):
        shutil.copy(label_path, train_label_dir)
        shutil.copy(img_path, train_image_dir)
    else:
        print(f"跳过 {label}：图像或标签文件不存在")

for label in val_labels:
    label_path = os.path.join(label_dir, label)
    img = label.replace('.txt', '.jpg')
    img_path = os.path.join(image_dir, img)
    
    # 检查图像和标签文件是否都存在
    if os.path.exists(label_path) and os.path.exists(img_path):
        shutil.copy(label_path, val_label_dir)
        shutil.copy(img_path, val_image_dir)
    else:
        print(f"跳过 {label}：图像或标签文件不存在")

print("数据划分完成！")

# 固定类别名
class_list = ['yj','sj','lj']
nc = len(class_list)

# 自动生成 YOLO yaml 配置文件
yaml_content = f'''# YOLO dataset config for {project_name}
train: dataset/images/train
val: dataset/images/val

nc: {nc}
names: {class_list}
'''
yaml_path = f'projects/{project_name}/{project_name}.yaml'
os.makedirs(f'projects/{project_name}', exist_ok=True)
with open(yaml_path, 'w') as f:
    f.write(yaml_content)
print(f"已生成 yaml 配置文件: {yaml_path}")
